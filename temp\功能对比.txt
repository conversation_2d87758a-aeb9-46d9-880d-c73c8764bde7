// MSSQL存储过程：
USE [hlyy]
GO
/****** Object:  StoredProcedure [dbo].[fx_yfyy_zs]    Script Date: 06/08/2025 12:00:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
--孕妇用药分析
-- drop proc [fx_yfyyfx]
ALTER PROCEDURE [dbo].[fx_yfyy_zs] 

--[fx_yfyy] '','380','55','',''
@code  nvarchar(50),
@akb020  nvarchar(20),
@yp_code nvarchar(20),
@pregnant_unit nvarchar(10),
@pregnant nvarchar(10)

--@yz      nvarchar(20)   ---孕周
with recompile
AS
declare @yz nvarchar(20) ---孕周
declare @nl nvarchar(20)
declare @id nvarchar(20)

--return
BEGIN
declare @ywa_name nvarchar(50)
select @ywa_name=DRUG_NAME from ITF_HOS_DRUG where DRUG_CODE =@yp_code

  --=1慎用
--=0禁用
--=2不明确
set @yz=dbo.fn_get_yz(@pregnant_unit,@pregnant)
if @yz <>''

select @code, @ywa_name ywa, '' ywb,'1' wtlvlcode,(select distinct d.wtlvl from wtlb d where d.wtlvlcode=a.bs) wtlvl,
case when a.bs='0' then  'RLT006' when a.bs='1' then 'RLT017' END AS wtcode,
case when a.bs='0' then  'RSQFNJJ' when a.bs='1' then 'RSQFNWT' END  as wtsp,
case when a.bs='0' then  '妊娠期妇女禁用' when a.bs='1' then '妊娠期妇女慎用'  END AS wtname,
'妊娠期用药问题' as title,
 '说明书提示：【'+cast(c.ym as varchar)+'】'+cast(c.yfyy as varchar(500))  as detail,0,'孕妇用药'
 from t_sda_gestation a,t_byyydzb b ,t_sda c
where a.sda_id=b.sda_id
and b.sda_id=c.ID 
and b.akb020 =@akb020
and b.yp_code=@yp_code
 and dayup<=@yz
 and daydown >=@yz
if @yz =''
select @code, @ywa_name ywa, '' ywb,'1' wtlvlcode,(select distinct d.wtlvl from wtlb d where d.wtlvlcode=a.bs) wtlvl,
case when a.bs='0' then  'RLT006' when a.bs='1' then 'RLT017' END AS wtcode,
case when a.bs='0' then  'RSQFNJJ' when a.bs='1' then 'RSQFNWT' END  as wtsp,
case when a.bs='0' then  '妊娠期妇女禁用' when a.bs='1' then '妊娠期妇女慎用'  END AS wtname,
'妊娠期用药问题' as title,
 '说明书提示：【'+cast(c.ym as varchar)+'】'+cast(c.yfyy as varchar(500))  as detail,0,'孕妇用药'
 from t_sda_gestation a,t_byyydzb b ,t_sda c
where a.sda_id=b.sda_id
and b.sda_id=c.ID 
and b.akb020 =@akb020
and b.yp_code=@yp_code
 and dayup<=-100
 and daydown >=1000



END










// MySQL存储过程：
CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_yfyy`(
    IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_pregnant_unit VARCHAR(10),
    IN p_pregnant VARCHAR(10)
)
    COMMENT '孕妇用药分析存储过程'
BEGIN
    DECLARE v_sda_id VARCHAR(20);
    DECLARE v_ywa_name VARCHAR(50);
    DECLARE v_yz INT DEFAULT 0;
    
    -- 异常处理
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 如果出现异常，继续执行
        BEGIN END;
    END;
    
    -- 获取药品名称
    SELECT DRUG_NAME INTO v_ywa_name
    FROM rms_itf_hos_drug 
    WHERE DRUG_CODE = p_yp_code
    LIMIT 1;
    
    -- 获取标准数据ID
    SELECT sda_id INTO v_sda_id
    FROM rms_t_byyydzb  
    WHERE akb020 = p_akb020 AND yp_code = p_yp_code
    LIMIT 1;
    
    -- 计算孕周天数
    SET v_yz = rms_fn_get_yz(p_pregnant_unit, p_pregnant);
    
    -- 如果有孕周信息，按孕周范围查询
    IF v_yz > 0 THEN
        INSERT INTO rms_t_pres_fx (
            Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
        )
        SELECT 
            p_Code,
            v_ywa_name AS ywa,
            '' AS ywb,
            '1' AS wtlvlcode,
            (SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.bs) AS wtlvl,
            CASE 
                WHEN a.bs = '0' THEN 'RLT006'
                WHEN a.bs = '1' THEN 'RLT017'
            END AS wtcode,
            CASE 
                WHEN a.bs = '0' THEN 'RSQFNJJ'
                WHEN a.bs = '1' THEN 'RSQFNWT'
            END AS wtsp,
            CASE 
                WHEN a.bs = '0' THEN '特殊人群禁用'
                WHEN a.bs = '1' THEN '特殊人群慎用'
            END AS wtname,
            '妊娠用药' AS title,
            CONCAT('说明书提示：', CAST(c.ym AS CHAR), CAST(c.yfyy AS CHAR(500))) AS detail,
            0,
            '孕妇用药'
        FROM rms_t_sda_gestation a, rms_t_byyydzb b, rms_t_sda c
        WHERE a.sda_id = b.sda_id
        AND b.sda_id = c.ID
        AND b.akb020 = p_akb020
        AND b.yp_code = p_yp_code
        AND a.dayup <= v_yz
        AND a.daydown >= v_yz;
    ELSE
        -- 如果没有孕周信息，查询默认范围
        INSERT INTO rms_t_pres_fx (
            Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
        )
        SELECT 
            p_Code,
            v_ywa_name AS ywa,
            '' AS ywb,
            '1' AS wtlvlcode,
            (SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.bs) AS wtlvl,
            CASE 
                WHEN a.bs = '0' THEN 'RLT006'
                WHEN a.bs = '1' THEN 'RLT017'
            END AS wtcode,
            CASE 
                WHEN a.bs = '0' THEN 'RSQFNJJ'
                WHEN a.bs = '1' THEN 'RSQFNWT'
            END AS wtsp,
            CASE 
                WHEN a.bs = '0' THEN '特殊人群禁用'
                WHEN a.bs = '1' THEN '特殊人群慎用'
            END AS wtname,
            '妊娠用药' AS title,
            CONCAT('说明书提示：', CAST(c.ym AS CHAR), CAST(c.yfyy AS CHAR(500))) AS detail,
            0,
            '孕妇用药'
        FROM rms_t_sda_gestation a, rms_t_byyydzb b, rms_t_sda c
        WHERE a.sda_id = b.sda_id
        AND b.sda_id = c.ID
        AND b.akb020 = p_akb020
        AND b.yp_code = p_yp_code
        AND a.dayup <= -100
        AND a.daydown >= 1000;
    END IF;
    
END