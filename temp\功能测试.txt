CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_lnr`(
    IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_csrq VARCHAR(20)
)
    COMMENT '老年人用药分析存储过程'
main_block: BEGIN
		DECLARE v_nl INT;
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_n_count_zy INT;
		DECLARE v_ywa_name VARCHAR(50);
		
		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				-- 如果出现异常，继续执行
				BEGIN END;
		END;
		
		-- 获取标准数据ID
		SELECT sda_id INTO v_sda_id 
		FROM rms_t_byyydzb 
		WHERE yp_code = p_yp_code
		LIMIT 1;
		
		-- 如果没有标准数据ID则返回
		IF v_sda_id = '' OR v_sda_id IS NULL THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否为中药
		SELECT COUNT(1) INTO v_n_count_zy 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND ZX_FLAG = '3';
		
		-- 如果是中药则返回
		IF v_n_count_zy > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 获取药品名称
		SELECT DRUG_NAME INTO v_ywa_name 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code
		LIMIT 1;
		
		-- 计算年龄（年数）
		-- 支持多种日期格式：YYYY-MM-DD 或 YYYY-M-D 或 YYYYMMDD
		SET v_nl = CASE 
				WHEN p_csrq LIKE '%-%' THEN 
						YEAR(CURDATE()) - YEAR(STR_TO_DATE(p_csrq, '%Y-%m-%d'))
				WHEN LENGTH(p_csrq) = 8 THEN
						YEAR(CURDATE()) - YEAR(STR_TO_DATE(p_csrq, '%Y%m%d'))
				ELSE
						YEAR(CURDATE()) - YEAR(STR_TO_DATE(p_csrq, '%Y-%m-%d'))
		END;
		
		-- 老年人用药分析：插入分析结果
		INSERT INTO rms_t_pres_fx (
				Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
		)
		SELECT 
				p_Code,
				v_ywa_name as ywa,
				'' as ywb,
				'1' as wtlvlcode,
				(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.jsbs LIMIT 1) as wtlvl,
				CASE 
						WHEN a.jsbs = 0 THEN 'RLT031' 
						WHEN a.jsbs = 1 THEN 'RLT032' 
						WHEN a.jsbs = 2 THEN 'RLT034' 
				END as wtcode,
				CASE 
						WHEN a.jsbs = 0 THEN 'LNRJJ' 
						WHEN a.jsbs = 1 THEN 'LNRWT' 
						WHEN a.jsbs = 2 THEN 'LNRJJTS' 
				END as wtsp,
				CASE 
						WHEN a.jsbs = 0 THEN '特殊人群禁用' 
						WHEN a.jsbs = 1 THEN '特殊人群慎用' 
						WHEN a.jsbs = 2 THEN '特殊人群提示' 
				END as wtname,
				CASE 
						WHEN a.jsbs = 0 THEN '老年人禁用药品' 
						WHEN a.jsbs = 1 THEN '老年人慎用药品' 
						WHEN a.jsbs = 2 THEN '老年人提示药品' 
				END as title,
				CASE 
						WHEN a.jsbs = 0 THEN CONCAT('说明书提示：', v_ywa_name, '老年人禁用！') 
						WHEN a.jsbs = 1 THEN CONCAT('说明书提示：', v_ywa_name, '老年人慎用！') 
						WHEN a.jsbs = 2 THEN CONCAT('说明书提示：', v_ywa_name, '老年人用药注意事项！') 
				END as detail,
				0 as flag,
				'老年人用药' as text
		FROM rms_t_sda_elder a
		WHERE a.sda_id = v_sda_id
		AND a.jsbs <> 9
		AND a.jsbs = 0
		AND a.age_min <= v_nl;
END